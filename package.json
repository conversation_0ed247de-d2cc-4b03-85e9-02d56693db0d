{"name": "video-renderer-server", "version": "1.0.0", "description": "视频加字幕应用后端系统 - 基于Node.js的视频处理服务", "main": "src/api/server.js", "scripts": {"dev": "concurrently \"nodemon src/api/server.js\" \"nodemon src/worker/index.js\"", "start": "node src/api/server.js", "worker": "node src/worker/index.js", "dev:api": "nodemon src/api/server.js", "dev:worker": "nodemon src/worker/index.js", "db:migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:studio": "npx prisma studio", "db:reset": "npx prisma migrate reset", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "prod": "pm2 start ecosystem.config.js --env production", "prod:stop": "pm2 stop ecosystem.config.js", "prod:restart": "pm2 restart ecosystem.config.js", "prod:logs": "pm2 logs"}, "keywords": ["video", "subtitle", "asr", "ffmpeg", "puppeteer", "nodejs", "express", "bullmq", "prisma"], "author": "Video Renderer Team", "license": "MIT", "dependencies": {"@prisma/client": "^5.6.0", "@volcengine/openapi": "^1.30.2", "@volcengine/tos-sdk": "^2.7.5", "bullmq": "^4.15.4", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "fluent-ffmpeg": "^2.1.2", "fs-extra": "^11.1.1", "helmet": "^7.1.0", "ioredis": "^5.3.2", "joi": "^17.11.0", "mime-types": "^2.1.35", "multer": "^1.4.5-lts.1", "path": "^0.12.7", "puppeteer": "^21.5.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@types/jest": "^29.5.8", "concurrently": "^8.2.2", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "nodemon": "^3.0.1", "pm2": "^5.3.0", "prettier": "^3.1.0", "prisma": "^5.6.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/video-renderer-server.git"}, "bugs": {"url": "https://github.com/your-org/video-renderer-server/issues"}, "homepage": "https://github.com/your-org/video-renderer-server#readme"}