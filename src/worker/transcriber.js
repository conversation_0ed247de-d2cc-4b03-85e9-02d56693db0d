/**
 * 火山引擎 ASR 语音识别集成模块
 * 实现与火山引擎豆包语音识别 API 的集成
 */

const fs = require('fs');
const path = require('path');
const { createModuleLogger } = require('../common/logger');
const config = require('../common/config');
const { getTosService } = require('../common/tosService');

const logger = createModuleLogger('transcriber');

// 验证火山引擎配置
function validateVolcengineConfig() {
  const requiredFields = {
    'appId': config.volcengine.appId,
    'accessKeyId': config.volcengine.accessKeyId,
    'secretAccessKey': config.volcengine.secretAccessKey,
    'apiKey': config.volcengine.apiKey,
    'region': config.volcengine.region
  };

  const missingFields = [];
  for (const [field, value] of Object.entries(requiredFields)) {
    if (!value) {
      missingFields.push(field);
    }
  }

  if (missingFields.length > 0) {
    logger.error('Missing Volcengine configuration', {
      missingFields,
      availableFields: Object.keys(requiredFields).filter(f => requiredFields[f])
    });
    throw new Error(`Missing Volcengine configuration: ${missingFields.join(', ')}`);
  }

  logger.info('Volcengine configuration validated', {
    appId: config.volcengine.appId,
    region: config.volcengine.region,
    endpoint: config.volcengine.endpoint,
    hasAccessKey: !!config.volcengine.accessKeyId,
    hasSecretKey: !!config.volcengine.secretAccessKey,
    hasApiKey: !!config.volcengine.apiKey
  });
}

// 在模块加载时验证配置
try {
  validateVolcengineConfig();
} catch (error) {
  logger.error('Volcengine configuration validation failed', { error: error.message });
}

/**
 * 解析火山引擎API状态码
 * @param {string} statusCode - 火山引擎API状态码
 * @returns {Object} 状态码信息
 */
function parseVolcengineStatusCode(statusCode) {
  const statusMap = {
    '20000000': { success: true, message: 'Success' },
    '40000001': { success: false, message: 'Invalid request parameters' },
    '40000002': { success: false, message: 'Authentication failed' },
    '40000003': { success: false, message: 'Insufficient permissions' },
    '40000004': { success: false, message: 'Rate limit exceeded' },
    '40000005': { success: false, message: 'Quota exceeded' },
    '50000001': { success: false, message: 'Internal server error' },
    '50000002': { success: false, message: 'Service temporarily unavailable' },
  };

  return statusMap[statusCode] || {
    success: false,
    message: `Unknown status code: ${statusCode}`
  };
}

// 火山引擎 ASR API 配置 - 大模型录音文件识别
const ASR_CONFIG = {
  endpoint: 'https://openspeech.bytedance.com',
  submitPath: '/api/v3/auc/bigmodel/submit',
  queryPath: '/api/v3/auc/bigmodel/query',
  resourceId: 'volc.bigasr.auc', // 固定资源ID，用于大模型录音文件识别
  model: 'bigmodel',
  maxFileSize: 100 * 1024 * 1024, // 100MB
  supportedFormats: ['wav', 'mp3', 'ogg', 'flac'],
  maxDuration: 3600, // 1小时
  timeout: 30000, // 30秒超时
};

/**
 * 生成火山引擎 API 签名 (AWS Signature V4)
 * @param {string} method - HTTP 方法
 * @param {string} path - API 路径
 * @param {Object} headers - 请求头
 * @param {string} body - 请求体
 * @returns {string} 签名
 */
function generateSignature(method, path, headers, body = '') {
  const crypto = require('crypto');

  const timestamp = headers['X-Date'] || new Date().toISOString().replace(/[:\-]|\.\d{3}/g, '');
  const date = timestamp.substr(0, 8);
  const region = config.volcengine.region;
  const service = 'speech';
  const credentialScope = `${date}/${region}/${service}/aws4_request`;

  // 1. 构建规范请求
  const canonicalHeaders = Object.keys(headers)
    .sort()
    .map(key => {
      const value = headers[key];
      const trimmedValue = typeof value === 'string' ? value.trim() : String(value).trim();
      return `${key.toLowerCase()}:${trimmedValue}`;
    })
    .join('\n') + '\n';

  const signedHeaders = Object.keys(headers)
    .sort()
    .map(key => key.toLowerCase())
    .join(';');

  const payloadHash = crypto.createHash('sha256').update(body, 'utf8').digest('hex');

  const canonicalRequest = [
    method,
    path,
    '', // 查询字符串
    canonicalHeaders,
    signedHeaders,
    payloadHash
  ].join('\n');

  // 2. 构建签名字符串
  const canonicalRequestHash = crypto.createHash('sha256').update(canonicalRequest, 'utf8').digest('hex');
  const stringToSign = [
    'AWS4-HMAC-SHA256',
    timestamp,
    credentialScope,
    canonicalRequestHash
  ].join('\n');

  // 3. 计算签名
  const kDate = crypto.createHmac('sha256', `AWS4${config.volcengine.secretAccessKey}`).update(date).digest();
  const kRegion = crypto.createHmac('sha256', kDate).update(region).digest();
  const kService = crypto.createHmac('sha256', kRegion).update(service).digest();
  const kSigning = crypto.createHmac('sha256', kService).update('aws4_request').digest();
  const signature = crypto.createHmac('sha256', kSigning).update(stringToSign, 'utf8').digest('hex');

  return signature;
}

/**
 * 发送 HTTP 请求到火山引擎 API
 * @param {string} method - HTTP 方法
 * @param {string} path - API 路径
 * @param {Object} data - 请求数据
 * @param {string} taskId - 任务ID (用作X-Api-Request-Id)，可选
 * @param {Object} options - 请求选项，可选
 * @returns {Promise<Object>} 响应数据
 */
async function sendRequest(method, path, data = null, taskId = null, options = {}) {
  const https = require('https');
  const url = require('url');
  
  const endpoint = new url.URL(ASR_CONFIG.endpoint + path);
  const body = data ? JSON.stringify(data) : '';
  
  // 🎯 关键修复：正确处理X-Api-Request-Id
  // 对于查询请求，使用传入的taskId；对于提交请求，生成新的UUID
  const requestId = taskId || require('uuid').v4();

  const headers = {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(body),
    'X-Date': new Date().toISOString().replace(/[:\-]|\.\d{3}/g, ''),
    'Host': endpoint.host,
    'X-Api-App-Key': config.volcengine.appId,
    'X-Api-Access-Key': config.volcengine.apiKey,
    'X-Api-Resource-Id': ASR_CONFIG.resourceId, // 必需的资源ID
    'X-Api-Request-Id': requestId, // 提交时生成新ID，查询时使用相同ID
    'X-Api-Sequence': '-1', // 固定值-1
  };

  // 生成签名
  const signature = generateSignature(method, path, headers, body);
  const timestamp = headers['X-Date'];
  const date = timestamp.substr(0, 8);
  const credentialScope = `${date}/${config.volcengine.region}/speech/aws4_request`;
  const signedHeaders = Object.keys(headers).sort().map(k => k.toLowerCase()).join(';');

  headers['Authorization'] = `AWS4-HMAC-SHA256 Credential=${config.volcengine.accessKeyId}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;

  // 记录请求信息（不包含敏感数据）
  logger.info('Sending ASR API request', {
    method,
    path,
    endpoint: endpoint.hostname,
    requestType: taskId ? 'QUERY (reusing X-Api-Request-Id)' : 'SUBMIT (new X-Api-Request-Id)',
    headers: {
      'Content-Type': headers['Content-Type'],
      'X-Api-App-Key': headers['X-Api-App-Key'],
      'X-Api-Resource-Id': headers['X-Api-Resource-Id'],
      'X-Api-Request-Id': headers['X-Api-Request-Id'],
      'X-Api-Sequence': headers['X-Api-Sequence'],
      'Authorization': headers['Authorization'] ? 'AWS4-HMAC-SHA256 [REDACTED]' : 'Missing'
    },
    bodyLength: body.length,
    isQueryRequest: !!taskId,
    taskIdReused: !!taskId
  });

  // 在debug级别记录完整的请求体（用于调试）
  logger.debug('ASR API request body', {
    body: body.length > 1000 ? body.substring(0, 1000) + '...[TRUNCATED]' : body,
    isEmpty: body.length === 0 || body === '{}',
    note: taskId ? 'Query request - body should be empty JSON {}' : 'Submit request - body contains audio data'
  });

  return new Promise((resolve, reject) => {
    const req = https.request({
      hostname: endpoint.hostname,
      port: endpoint.port || 443,
      path: endpoint.pathname + endpoint.search,
      method,
      headers,
      timeout: options.timeout || 30000,
    }, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        // 火山引擎ASR API的状态信息在响应头中，不在响应体中
        const volcengineHeaders = {
          logId: res.headers['x-tt-logid'],
          statusCode: res.headers['x-api-status-code'],
          message: res.headers['x-api-message'],
          requestId: res.headers['x-api-request-id']
        };

        logger.info('ASR API response received', {
          httpStatusCode: res.statusCode,
          volcengineHeaders,
          responseBodyLength: responseData.length,
          path,
          method
        });

        // 检查HTTP状态码
        if (res.statusCode >= 200 && res.statusCode < 300) {
          // 检查火山引擎特定的状态码
          const volcStatusCode = volcengineHeaders.statusCode;
          const volcMessage = volcengineHeaders.message;

          // 解析火山引擎状态码
          const statusInfo = parseVolcengineStatusCode(volcStatusCode);

          if (statusInfo.success || volcMessage === 'OK') {
            // 成功响应
            logger.info('ASR API request successful', {
              httpStatusCode: res.statusCode,
              volcStatusCode,
              volcMessage,
              logId: volcengineHeaders.logId,
              path,
              method
            });

            // 尝试解析响应体（如果有的话）
            let responseBody = {};
            if (responseData.trim()) {
              try {
                responseBody = JSON.parse(responseData);
              } catch (parseError) {
                logger.warn('Failed to parse response body, but API call was successful', {
                  parseError: parseError.message,
                  responseData: responseData.substring(0, 200)
                });
              }
            }

            // 返回包含火山引擎头信息的结果
            resolve({
              ...responseBody,
              volcengineHeaders,
              success: true
            });
          } else {
            // 火山引擎API返回了错误状态
            const errorInfo = {
              httpStatusCode: res.statusCode,
              volcStatusCode,
              volcMessage,
              logId: volcengineHeaders.logId,
              requestId: volcengineHeaders.requestId
            };

            logger.error('ASR API returned error status', {
              path,
              method,
              errorInfo,
              responseData: responseData.substring(0, 500)
            });

            reject(new Error(`ASR API error: [${volcStatusCode}] ${volcMessage} (LogId: ${volcengineHeaders.logId})`));
          }
        } else {
          // HTTP错误
          const errorInfo = {
            httpStatusCode: res.statusCode,
            volcStatusCode: volcengineHeaders.statusCode,
            volcMessage: volcengineHeaders.message,
            logId: volcengineHeaders.logId
          };

          logger.error('ASR API HTTP request failed', {
            path,
            method,
            errorInfo,
            responseData: responseData.substring(0, 500)
          });

          reject(new Error(`HTTP ${res.statusCode}: ${volcengineHeaders.message || 'Unknown HTTP error'} (LogId: ${volcengineHeaders.logId})`));
        }
      });
    });

    req.on('error', (error) => {
      reject(new Error(`Request failed: ${error.message}`));
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (body) {
      req.write(body);
    }
    
    req.end();
  });
}

/**
 * 上传音频文件到TOS并获取公共URL
 * @param {string} audioPath - 音频文件路径
 * @param {string} dbId - 数据库ID，用于生成唯一的TOS键
 * @returns {Promise<Object>} 上传结果包含URL和元数据
 */
async function uploadAudioToTos(audioPath, dbId) {
  try {
    const tosService = getTosService();
    const absoluteAudioPath = require('path').resolve(audioPath);

    // 获取文件信息
    const stats = fs.statSync(audioPath);
    const fileSize = stats.size;
    const fileExt = path.extname(audioPath).toLowerCase().slice(1);
    const originalName = path.basename(audioPath);

    logger.info('🚀 Starting audio upload to TOS', {
      step: '5-TOS-UPLOAD-START',
      audioPath: absoluteAudioPath,
      fileSize,
      fileSizeMB: (fileSize / 1024 / 1024).toFixed(2),
      format: fileExt,
      dbId
    });

    // 生成TOS对象键
    const tosKey = tosService.generateAudioKey(originalName, dbId);

    // 上传文件到TOS
    const uploadResult = await tosService.uploadFileFromPath(
      tosKey,
      audioPath,
      `audio/${fileExt}`,
      {
        dbId: dbId,
        originalName: originalName,
        uploadTime: new Date().toISOString(),
        purpose: 'asr-processing'
      }
    );

    logger.info('✅ Audio uploaded to TOS successfully', {
      step: '5-TOS-UPLOAD-SUCCESS',
      audioPath: absoluteAudioPath,
      tosKey: uploadResult.key,
      tosUrl: uploadResult.url,
      fileSize: uploadResult.size,
      fileSizeMB: (uploadResult.size / 1024 / 1024).toFixed(2),
      etag: uploadResult.etag
    });

    return uploadResult;

  } catch (error) {
    logger.error('❌ Audio upload to TOS failed', {
      step: '5-TOS-UPLOAD-ERROR',
      audioPath,
      dbId,
      error: error.message
    });
    throw new Error(`Audio upload to TOS failed: ${error.message}`);
  }
}

/**
 * 上传音频文件并提交转录任务
 * @param {string} audioPath - 音频文件路径
 * @param {Object} options - 转录选项
 * @param {string} dbId - 数据库ID，用于TOS文件管理
 * @returns {Promise<string>} 任务ID
 */
async function submitTranscriptionTask(audioPath, options = {}, dbId = null) {
  try {
    // 🔍 调试：检查options参数
    logger.debug('submitTranscriptionTask called with parameters', {
      audioPath,
      options,
      optionsType: typeof options,
      optionsKeys: Object.keys(options || {})
    });

    // 检查文件是否存在
    if (!fs.existsSync(audioPath)) {
      throw new Error(`Audio file not found: ${audioPath}`);
    }

    // 获取文件信息
    const stats = fs.statSync(audioPath);
    const fileSize = stats.size;
    const fileExt = path.extname(audioPath).toLowerCase().slice(1);

    // 验证文件大小
    if (fileSize > ASR_CONFIG.maxFileSize) {
      throw new Error(`File size ${fileSize} exceeds maximum ${ASR_CONFIG.maxFileSize}`);
    }

    // 验证文件格式
    if (!ASR_CONFIG.supportedFormats.includes(fileExt)) {
      throw new Error(`Unsupported audio format: ${fileExt}`);
    }

    // 上传音频文件到TOS并获取公共URL
    const absoluteAudioPath = require('path').resolve(audioPath);

    // 如果没有提供dbId，生成一个临时ID
    const uploadDbId = dbId || `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    logger.info('🔄 Uploading audio to TOS for ASR processing', {
      step: '5-ASR-TOS-UPLOAD',
      audioPath: absoluteAudioPath,
      fileSize,
      fileSizeMB: (fileSize / 1024 / 1024).toFixed(2),
      format: fileExt,
      dbId: uploadDbId
    });

    // 上传到TOS并获取公共URL
    const tosUploadResult = await uploadAudioToTos(audioPath, uploadDbId);

    logger.info('🚀 Submitting transcription task to Volcengine ASR API', {
      step: '5-ASR-SUBMIT',
      audioPath: absoluteAudioPath,
      fileSize: tosUploadResult.size,
      fileSizeMB: (tosUploadResult.size / 1024 / 1024).toFixed(2),
      format: fileExt,
      tosUrl: tosUploadResult.url,
      tosKey: tosUploadResult.key
    });

    // 构建请求数据 - 符合火山引擎大模型录音文件识别API规范（使用TOS URL）
    const requestData = {
      user: {
        uid: "video_processor_user" // 用户标识
      },
      audio: {
        format: fileExt, // 音频格式
        url: tosUploadResult.url, // 使用TOS公共URL
      },
      request: {
        model_name: ASR_CONFIG.model, // 固定值 "bigmodel"
        enable_itn: options.enableItn !== false, // 文本规范化
        enable_punc: options.enablePunc !== false, // 标点符号
        enable_ddc: options.enableDdc || false, // 语义顺滑
        show_utterances: options.showUtterances !== false, // 分句信息
        language: options.language || 'zh-CN', // 语言
      }
    };

    logger.info('📤 ASR API request data structure (TOS URL format)', {
      step: '5-ASR-REQUEST-DATA',
      audioPath: absoluteAudioPath,
      audioFormat: fileExt,
      audioSizeBytes: tosUploadResult.size,
      tosUrl: tosUploadResult.url,
      tosKey: tosUploadResult.key,
      modelName: ASR_CONFIG.model,
      language: options.language || 'zh-CN',
      requestDataKeys: Object.keys(requestData),
      audioKeys: Object.keys(requestData.audio),
      requestKeys: Object.keys(requestData.request),
      userKeys: Object.keys(requestData.user)
    });

    // 发送请求
    const response = await sendRequest('POST', ASR_CONFIG.submitPath, requestData, null, { timeout: ASR_CONFIG.timeout });

    // 详细记录完整的API响应用于调试
    logger.info('🔍 Volcengine ASR API submission response analysis', {
      step: '5-ASR-SUBMIT-RESPONSE',
      audioPath: absoluteAudioPath,
      httpSuccess: response.success,
      volcengineHeaders: response.volcengineHeaders,
      responseBodyKeys: Object.keys(response).filter(key => key !== 'volcengineHeaders'),
      fullResponse: JSON.stringify(response, null, 2)
    });

    // 记录完整的API响应用于调试
    logger.debug('ASR API submit response', {
      audioPath,
      response: JSON.stringify(response, null, 2)
    });

    // 检查响应状态（火山引擎API成功时response.success为true）
    if (!response.success) {
      // 这种情况不应该发生，因为sendRequest已经处理了错误
      throw new Error('ASR API request failed but no error was thrown');
    }

    // 🎯 关键修复：任务ID就是我们发送的X-Api-Request-Id
    // 根据火山引擎API文档，X-Api-Request-Id既是请求标识符也是任务标识符
    const taskId = response.volcengineHeaders?.requestId;

    if (!taskId) {
      logger.error('❌ No X-Api-Request-Id found in response headers', {
        step: '5-ASR-TASKID-ERROR',
        audioPath: absoluteAudioPath,
        logId: response.volcengineHeaders?.logId,
        volcengineHeaders: response.volcengineHeaders,
        suggestion: 'X-Api-Request-Id header is missing from response'
      });
      throw new Error('No X-Api-Request-Id found in response headers');
    }

    logger.info('✅ ASR task submitted successfully', {
      step: '5-ASR-SUBMIT-SUCCESS',
      audioPath: absoluteAudioPath,
      taskId: taskId,
      taskIdSource: 'X-Api-Request-Id header (sent in request, returned in response)',
      logId: response.volcengineHeaders?.logId,
      statusCode: response.volcengineHeaders?.statusCode,
      message: response.volcengineHeaders?.message,
      note: 'Will use this same X-Api-Request-Id for querying task status'
    });

    logger.info('Transcription task submitted successfully', {
      audioPath,
      taskId: taskId,
      mode: 'asynchronous',
      note: 'Task ID is the X-Api-Request-Id used for both submission and querying'
    });

    // 返回任务ID和TOS文件信息，用于后续清理
    return {
      taskId,
      tosFile: {
        key: tosUploadResult.key,
        url: tosUploadResult.url,
        size: tosUploadResult.size,
        etag: tosUploadResult.etag
      }
    };

  } catch (error) {
    logger.error('Failed to submit transcription task', {
      audioPath,
      error: error.message,
    });
    throw error;
  }
}

/**
 * 查询转录任务状态和结果
 * @param {string} taskId - 任务ID
 * @returns {Promise<Object>} 任务状态和结果
 */
async function queryTranscriptionResult(taskId) {
  try {
    logger.info('🔍 Querying transcription result from Volcengine ASR API', {
      step: '6-ASR-QUERY-START',
      taskId,
      queryEndpoint: ASR_CONFIG.queryPath,
      note: 'Using X-Api-Request-Id from submission as task identifier'
    });

    // 🎯 关键修复：根据火山引擎API文档，查询请求体应该为空JSON
    const requestData = {};

    // 发送查询请求，taskId将作为X-Api-Request-Id头部发送
    const response = await sendRequest('POST', ASR_CONFIG.queryPath, requestData, taskId, { timeout: ASR_CONFIG.timeout });

    // 详细记录完整的API响应用于调试
    logger.info('📋 Volcengine ASR API query response analysis', {
      step: '6-ASR-QUERY-RESPONSE',
      taskId,
      httpSuccess: response.success,
      volcengineHeaders: response.volcengineHeaders,
      responseBodyKeys: Object.keys(response).filter(key => key !== 'volcengineHeaders'),
      fullResponse: JSON.stringify(response, null, 2)
    });

    // 检查响应状态（火山引擎API成功时response.success为true）
    if (!response.success) {
      // 这种情况不应该发生，因为sendRequest已经处理了错误
      throw new Error('ASR API query failed but no error was thrown');
    }

    logger.info('✅ ASR query completed successfully', {
      step: '6-ASR-QUERY-SUCCESS',
      taskId,
      logId: response.volcengineHeaders?.logId,
      statusCode: response.volcengineHeaders?.statusCode,
      message: response.volcengineHeaders?.message
    });

    // 对于火山引擎API，查询结果也可能在响应体中（如果有的话）
    const responseData = response.data || response;

    const result = {
      taskId,
      status: responseData?.status || 'unknown',
      progress: responseData?.progress || 0,
      result: null,
      error: null,
      logId: response.volcengineHeaders?.logId
    };

    // 处理不同状态
    switch (result.status) {
      case 'Success':
        result.result = parseTranscriptionResult(responseData?.result);
        logger.info('Transcription completed successfully', {
          taskId,
          wordCount: result.result?.words?.length || 0,
          logId: result.logId
        });
        break;

      case 'Failed':
        result.error = responseData?.error_msg || 'Transcription failed';
        logger.error('Transcription failed', {
          taskId,
          error: result.error,
          logId: result.logId
        });
        break;

      case 'Processing':
        logger.debug('Transcription in progress', {
          taskId,
          progress: result.progress,
          logId: result.logId
        });
        break;
        
      default:
        logger.debug('Transcription status unknown', {
          taskId,
          status: result.status,
        });
    }

    return result;

  } catch (error) {
    logger.error('Failed to query transcription result', {
      taskId,
      error: error.message,
    });
    throw error;
  }
}

/**
 * 解析转录结果
 * @param {Object} rawResult - 原始转录结果
 * @returns {Object} 格式化的转录结果
 */
function parseTranscriptionResult(rawResult) {
  if (!rawResult) {
    return null;
  }

  try {
    // 解析转录结果，提取文本和时间戳信息
    const result = {
      text: rawResult.text || '',
      words: [],
      sentences: [],
      duration: 0,
    };

    // 处理词级别的结果
    if (rawResult.words && Array.isArray(rawResult.words)) {
      result.words = rawResult.words.map(word => ({
        text: word.text || word.word,
        startTime: parseFloat(word.start_time || word.startTime || 0),
        endTime: parseFloat(word.end_time || word.endTime || 0),
        confidence: parseFloat(word.confidence || 1.0),
      }));
    }

    // 处理句子级别的结果
    if (rawResult.utterances && Array.isArray(rawResult.utterances)) {
      result.sentences = rawResult.utterances.map(utterance => ({
        text: utterance.text,
        startTime: parseFloat(utterance.start_time || 0),
        endTime: parseFloat(utterance.end_time || 0),
        words: utterance.words || [],
      }));
    }

    // 计算总时长
    if (result.words.length > 0) {
      result.duration = Math.max(...result.words.map(w => w.endTime));
    }

    return result;

  } catch (error) {
    logger.error('Failed to parse transcription result', {
      error: error.message,
      rawResult,
    });
    throw error;
  }
}

/**
 * 轮询等待转录完成
 * @param {string} taskId - 任务ID
 * @param {Object} options - 轮询选项
 * @returns {Promise<Object>} 最终转录结果
 */
async function waitForTranscription(taskId, options = {}) {
  const {
    maxAttempts = 60, // 最大尝试次数
    interval = 5000, // 轮询间隔 (毫秒)
    timeout = 300000, // 总超时时间 (毫秒)
  } = options;

  const startTime = Date.now();
  let attempts = 0;

  logger.info('Starting transcription polling', {
    taskId,
    maxAttempts,
    interval,
    timeout,
    note: 'Using X-Api-Request-Id from submission for querying'
  });

  while (attempts < maxAttempts) {
    // 检查总超时
    if (Date.now() - startTime > timeout) {
      throw new Error(`Transcription timeout after ${timeout}ms`);
    }

    try {
      const result = await queryTranscriptionResult(taskId);
      
      if (result.status === 'Success') {
        return result;
      }
      
      if (result.status === 'Failed') {
        throw new Error(`Transcription failed: ${result.error}`);
      }

      // 等待下次轮询
      attempts++;
      if (attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, interval));
      }

    } catch (error) {
      if (attempts >= maxAttempts - 1) {
        throw error;
      }
      
      logger.warn('Transcription query failed, retrying', {
        taskId,
        attempt: attempts + 1,
        error: error.message,
      });
      
      attempts++;
      await new Promise(resolve => setTimeout(resolve, interval));
    }
  }

  throw new Error(`Transcription polling exceeded maximum attempts: ${maxAttempts}`);
}

/**
 * 清理TOS中的音频文件
 * @param {string} tosKey - TOS对象键
 * @returns {Promise<void>}
 */
async function cleanupTosAudio(tosKey) {
  try {
    const tosService = getTosService();

    logger.info('🧹 Cleaning up TOS audio file', {
      step: 'TOS-CLEANUP-START',
      tosKey
    });

    await tosService.deleteFile(tosKey);

    logger.info('✅ TOS audio file cleaned up successfully', {
      step: 'TOS-CLEANUP-SUCCESS',
      tosKey
    });

  } catch (error) {
    logger.warn('⚠️ Failed to cleanup TOS audio file', {
      step: 'TOS-CLEANUP-WARNING',
      tosKey,
      error: error.message,
      note: 'This is not critical, file may have already been deleted or expired'
    });
  }
}

module.exports = {
  submitTranscriptionTask,
  queryTranscriptionResult,
  waitForTranscription,
  parseTranscriptionResult,
  uploadAudioToTos,
  cleanupTosAudio,
  ASR_CONFIG,
};
