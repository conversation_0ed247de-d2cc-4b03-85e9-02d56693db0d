/**
 * 视频处理核心逻辑
 * 整合完整的视频处理流程：音频提取 -> 语音识别 -> 字幕渲染 -> 视频合成
 */

const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');

const { createModuleLogger } = require('../common/logger');
const { prisma } = require('../common/prisma');
const config = require('../common/config');

// 导入处理模块
const {
  getVideoInfo,
  extractAudio,
  mergeVideoAudio,
  convertVideo,
  generateThumbnail,
  validateAudioFormat,
  cleanupFiles,
} = require('./ffmpegProcessor');

const {
  submitTranscriptionTask,
  waitForTranscription,
} = require('./transcriber');

const {
  renderSubtitles,
} = require('./subtitleRenderer');

const logger = createModuleLogger('videoProcessor');

/**
 * 视频处理器类
 */
class VideoProcessor {
  constructor(jobData) {
    this.jobData = jobData;
    this.dbId = jobData.dbId;
    this.filePath = jobData.filePath;
    this.originalName = jobData.originalName;
    this.tempFiles = []; // 临时文件列表，用于最后清理
    this.processingSteps = [
      { name: 'validate', progress: 5, description: '验证视频文件' },
      { name: 'extract_audio', progress: 20, description: '提取音频' },
      { name: 'transcribe', progress: 50, description: '语音识别' },
      { name: 'render_subtitles', progress: 80, description: '渲染字幕' },
      { name: 'merge_video', progress: 95, description: '合成视频' },
      { name: 'finalize', progress: 100, description: '完成处理' },
    ];
  }

  /**
   * 执行完整的视频处理流程
   * @returns {Promise<Object>} 处理结果
   */
  async process() {
    try {
      logger.info('Starting video processing', {
        dbId: this.dbId,
        originalName: this.originalName,
        filePath: this.filePath,
      });

      // 更新任务状态为处理中
      await this.updateJobStatus('PROCESSING', 0, '开始处理视频');

      // 执行处理步骤
      const result = await this.executeProcessingSteps();

      // 更新任务状态为完成
      await this.updateJobStatus('COMPLETED', 100, '视频处理完成', {
        finalVideoUrl: result.finalVideoUrl,
        subtitlesJson: result.subtitles,
        duration: result.duration,
      });

      logger.info('Video processing completed successfully', {
        dbId: this.dbId,
        finalVideoUrl: result.finalVideoUrl,
        duration: result.duration,
      });

      return result;

    } catch (error) {
      logger.error('Video processing failed', {
        dbId: this.dbId,
        error: error.message,
        stack: error.stack,
      });

      // 更新任务状态为失败
      await this.updateJobStatus('FAILED', 0, '视频处理失败', {
        errorMessage: error.message,
        errorCode: error.code || 'PROCESSING_ERROR',
      });

      throw error;

    } finally {
      // 清理临时文件
      await this.cleanupTempFiles();
    }
  }

  /**
   * 执行处理步骤
   * @returns {Promise<Object>} 处理结果
   */
  async executeProcessingSteps() {
    const results = {};

    // 1. 验证文件
    await this.updateProgress('validate');
    results.videoInfo = await this.validateVideo();

    // 检查是否是纯音频文件
    const isAudioOnly = !results.videoInfo.video && results.videoInfo.audio;

    // 2. 提取/处理音频
    await this.updateProgress('extract_audio');
    results.audioPath = await this.extractAudioFromVideo();

    // 3. 语音识别
    await this.updateProgress('transcribe');
    results.subtitles = await this.transcribeAudio(results.audioPath);

    if (isAudioOnly) {
      // 对于纯音频文件，只进行转录，不需要视频处理
      logger.info('Audio-only processing completed', {
        dbId: this.dbId,
        transcriptionResult: results.subtitles ? 'success' : 'failed'
      });

      // 直接返回转录结果
      await this.updateProgress('completed');
      return {
        ...results,
        finalVideoPath: null, // 纯音频文件没有最终视频
        finalVideoUrl: null,
        duration: results.videoInfo.duration,
        isAudioOnly: true
      };
    } else {
      // 对于视频文件，继续完整的处理流程
      // 4. 渲染字幕
      await this.updateProgress('render_subtitles');
      results.subtitleVideoPath = await this.renderSubtitleVideo(results.subtitles, results.videoInfo);

      // 5. 合成最终视频
      await this.updateProgress('merge_video');
      results.finalVideoPath = await this.mergeVideoWithAudio(
        results.subtitleVideoPath,
        results.audioPath
      );

      // 6. 完成处理
      await this.updateProgress('finalize');
      results.finalVideoUrl = await this.finalizeVideo(results.finalVideoPath);
      results.duration = results.videoInfo.duration;
      results.isAudioOnly = false;

      return results;
    }
  }

  /**
   * 验证视频文件
   * @returns {Promise<Object>} 视频信息
   */
  async validateVideo() {
    try {
      // 检查文件是否存在
      const absolutePath = require('path').resolve(this.filePath);

      logger.info('📂 Starting file validation', {
        step: '2-VALIDATE-START',
        dbId: this.dbId,
        relativePath: this.filePath,
        absolutePath: absolutePath
      });

      await fs.access(this.filePath);

      // 获取文件系统信息
      const stats = await fs.stat(this.filePath);
      logger.info('📊 File system information', {
        step: '2-VALIDATE-STATS',
        dbId: this.dbId,
        path: absolutePath,
        size: stats.size,
        sizeMB: (stats.size / 1024 / 1024).toFixed(2),
        isFile: stats.isFile(),
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime,
        permissions: stats.mode.toString(8)
      });

      // 获取视频信息
      logger.info('🎬 Analyzing video/audio content with FFmpeg', {
        step: '2-VALIDATE-FFMPEG',
        dbId: this.dbId,
        path: absolutePath
      });

      const videoInfo = await getVideoInfo(this.filePath);

      logger.info('📋 FFmpeg analysis results', {
        step: '2-VALIDATE-RESULT',
        dbId: this.dbId,
        path: absolutePath,
        format: videoInfo.format,
        duration: videoInfo.duration,
        hasVideo: !!videoInfo.video,
        hasAudio: !!videoInfo.audio,
        videoInfo: videoInfo.video ? {
          codec: videoInfo.video.codec,
          width: videoInfo.video.width,
          height: videoInfo.video.height,
          fps: videoInfo.video.fps
        } : null,
        audioInfo: videoInfo.audio ? {
          codec: videoInfo.audio.codec,
          sampleRate: videoInfo.audio.sampleRate,
          channels: videoInfo.audio.channels,
          bitrate: videoInfo.audio.bitrate
        } : null
      });

      // 验证文件格式
      // 对于ASR调试，允许纯音频文件
      const isAudioOnly = !videoInfo.video && videoInfo.audio;
      const isVideoWithAudio = videoInfo.video && videoInfo.audio;

      if (!isAudioOnly && !isVideoWithAudio) {
        if (!videoInfo.video && !videoInfo.audio) {
          throw new Error('No video or audio stream found in file');
        } else if (!videoInfo.audio) {
          throw new Error('No audio stream found in file (required for transcription)');
        }
      }

      // 记录文件类型
      const fileType = isAudioOnly ? 'audio-only' : 'video-with-audio';
      logger.info('File type detected', {
        dbId: this.dbId,
        fileType,
        hasVideo: !!videoInfo.video,
        hasAudio: !!videoInfo.audio
      });

      if (videoInfo.duration <= 0) {
        throw new Error('Invalid video duration');
      }

      // 检查视频时长限制
      const maxDuration = 3600; // 1小时
      if (videoInfo.duration > maxDuration) {
        throw new Error(`Video duration ${videoInfo.duration}s exceeds maximum ${maxDuration}s`);
      }

      logger.info('File validation completed', {
        dbId: this.dbId,
        duration: videoInfo.duration,
        format: videoInfo.format,
        resolution: videoInfo.video ? `${videoInfo.video.width}x${videoInfo.video.height}` : 'audio-only',
        fileType: isAudioOnly ? 'audio-only' : 'video-with-audio'
      });

      return videoInfo;

    } catch (error) {
      logger.error('Video validation failed', {
        dbId: this.dbId,
        error: error.message,
      });
      throw new Error(`Video validation failed: ${error.message}`);
    }
  }

  /**
   * 从视频中提取音频或处理纯音频文件
   * @returns {Promise<string>} 音频文件路径
   */
  async extractAudioFromVideo() {
    try {
      const audioFileName = `${uuidv4()}_audio.wav`;
      const audioPath = path.join(config.paths.outputDir, audioFileName);
      const absoluteAudioPath = require('path').resolve(audioPath);
      this.tempFiles.push(audioPath);

      logger.info('🎵 Starting audio extraction/processing', {
        step: '3-AUDIO-START',
        dbId: this.dbId,
        inputPath: require('path').resolve(this.filePath),
        outputPath: absoluteAudioPath,
        outputFilename: audioFileName
      });

      // 检查是否是纯音频文件
      const videoInfo = await getVideoInfo(this.filePath);
      const isAudioOnly = !videoInfo.video && videoInfo.audio;

      if (isAudioOnly) {
        // 如果是纯音频文件，直接转换格式（如果需要）
        logger.info('🎧 Processing audio-only file', {
          step: '3-AUDIO-ONLY',
          dbId: this.dbId,
          inputPath: require('path').resolve(this.filePath),
          outputPath: absoluteAudioPath,
          originalFormat: videoInfo.format,
          targetFormat: 'wav',
          targetSampleRate: 16000,
          targetChannels: 1
        });

        // 转换为ASR所需的格式（WAV, 16kHz, 单声道）
        await extractAudio(this.filePath, audioPath, {
          format: 'wav',
          sampleRate: 16000,
          channels: 1,
        });
      } else {
        // 如果是视频文件，提取音频
        logger.info('🎬➡️🎵 Extracting audio from video file', {
          step: '3-AUDIO-EXTRACT',
          dbId: this.dbId,
          inputPath: require('path').resolve(this.filePath),
          outputPath: absoluteAudioPath,
          videoFormat: videoInfo.format,
          videoDuration: videoInfo.duration,
          targetFormat: 'wav',
          targetSampleRate: 16000,
          targetChannels: 1
        });

        await extractAudio(this.filePath, audioPath, {
          format: 'wav',
          sampleRate: 16000,
          channels: 1,
        });
      }

      // 验证生成的音频文件
      if (await fs.access(audioPath).then(() => true).catch(() => false)) {
        const audioStats = await fs.stat(audioPath);
        logger.info('✅ Audio file generated successfully', {
          step: '3-AUDIO-SUCCESS',
          dbId: this.dbId,
          outputPath: absoluteAudioPath,
          fileSize: audioStats.size,
          fileSizeMB: (audioStats.size / 1024 / 1024).toFixed(2),
          createdAt: audioStats.birthtime,
          isAudioOnly
        });
      } else {
        throw new Error('Audio file was not created');
      }

      // 验证音频格式
      logger.info('🔍 Validating audio format for ASR compatibility', {
        step: '3-AUDIO-VALIDATE',
        dbId: this.dbId,
        audioPath: absoluteAudioPath
      });

      const isValid = await validateAudioFormat(audioPath);
      if (!isValid) {
        logger.error('❌ Generated audio format is invalid for ASR', {
          step: '3-AUDIO-VALIDATE-FAIL',
          dbId: this.dbId,
          audioPath: absoluteAudioPath
        });
        throw new Error('Generated audio format is invalid for ASR');
      }

      // 获取最终音频文件的详细信息
      const finalAudioInfo = await getVideoInfo(audioPath);
      logger.info('✅ Audio processing completed successfully', {
        step: '3-AUDIO-COMPLETE',
        dbId: this.dbId,
        audioPath: absoluteAudioPath,
        audioInfo: {
          format: finalAudioInfo.format,
          duration: finalAudioInfo.duration,
          codec: finalAudioInfo.audio?.codec,
          sampleRate: finalAudioInfo.audio?.sampleRate,
          channels: finalAudioInfo.audio?.channels,
          bitrate: finalAudioInfo.audio?.bitrate
        },
        isAudioOnly,
        readyForASR: true
      });

      return audioPath;

    } catch (error) {
      logger.error('Audio processing failed', {
        dbId: this.dbId,
        error: error.message,
      });
      throw new Error(`Audio processing failed: ${error.message}`);
    }
  }

  /**
   * 转录音频
   * @param {string} audioPath - 音频文件路径
   * @returns {Promise<Object>} 转录结果
   */
  async transcribeAudio(audioPath) {
    try {
      const absoluteAudioPath = require('path').resolve(audioPath);

      logger.info('🎤 Starting audio transcription process', {
        step: '4-TRANSCRIBE-START',
        dbId: this.dbId,
        audioPath: absoluteAudioPath
      });

      // 在发送到ASR API之前，最后一次验证音频文件
      if (await fs.access(audioPath).then(() => true).catch(() => false)) {
        const audioStats = await fs.stat(audioPath);
        const audioInfo = await getVideoInfo(audioPath);

        logger.info('📊 Pre-transcription audio file verification', {
          step: '4-TRANSCRIBE-VERIFY',
          dbId: this.dbId,
          audioPath: absoluteAudioPath,
          fileExists: true,
          fileSize: audioStats.size,
          fileSizeMB: (audioStats.size / 1024 / 1024).toFixed(2),
          duration: audioInfo.duration,
          format: audioInfo.format,
          audioCodec: audioInfo.audio?.codec,
          sampleRate: audioInfo.audio?.sampleRate,
          channels: audioInfo.audio?.channels,
          bitrate: audioInfo.audio?.bitrate
        });

        // 检查文件是否为空
        if (audioStats.size === 0) {
          throw new Error('Audio file is empty (0 bytes)');
        }

        // 检查音频时长
        if (!audioInfo.duration || audioInfo.duration <= 0) {
          throw new Error('Audio file has invalid duration');
        }

      } else {
        logger.error('❌ Audio file not found before transcription', {
          step: '4-TRANSCRIBE-VERIFY-FAIL',
          dbId: this.dbId,
          audioPath: absoluteAudioPath
        });
        throw new Error('Audio file not found before transcription');
      }

      logger.info('🚀 Submitting transcription task to ASR API', {
        step: '4-TRANSCRIBE-SUBMIT',
        dbId: this.dbId,
        audioPath: absoluteAudioPath,
        options: {
          enableItn: true,
          enablePunc: true,
          showUtterances: true,
          language: 'zh-CN'
        }
      });

      // 提交转录任务
      const taskId = await submitTranscriptionTask(audioPath, {
        enableItn: true,
        enablePunc: true,
        showUtterances: true,
        language: 'zh-CN',
      });

      logger.info('✅ Transcription task submitted successfully', {
        step: '4-TRANSCRIBE-SUBMITTED',
        dbId: this.dbId,
        audioPath: absoluteAudioPath,
        taskId,
      });

      // 等待转录完成
      const result = await waitForTranscription(taskId, {
        maxAttempts: 120, // 10分钟
        interval: 5000,
        timeout: 600000,
      });

      if (!result.result) {
        throw new Error('No transcription result received');
      }

      // 转换为字幕格式
      const subtitles = this.convertToSubtitleFormat(result.result);

      logger.info('Audio transcription completed', {
        dbId: this.dbId,
        taskId,
        wordCount: subtitles.length,
        duration: result.result.duration,
      });

      return subtitles;

    } catch (error) {
      logger.error('Audio transcription failed', {
        dbId: this.dbId,
        error: error.message,
      });

      // 即使失败也标记ASR完成，以便清理音频文件和TOS文件
      this.asrCompleted = true;
      await this.cleanupAudioFiles();
      await this.cleanupTosFiles();

      throw new Error(`Audio transcription failed: ${error.message}`);
    }
  }

  /**
   * 转换转录结果为字幕格式
   * @param {Object} transcriptionResult - 转录结果
   * @returns {Array} 字幕数组
   */
  convertToSubtitleFormat(transcriptionResult) {
    const subtitles = [];

    if (transcriptionResult.sentences && transcriptionResult.sentences.length > 0) {
      // 使用句子级别的结果
      transcriptionResult.sentences.forEach((sentence, index) => {
        subtitles.push({
          id: index + 1,
          text: sentence.text.trim(),
          startTime: sentence.startTime,
          endTime: sentence.endTime,
        });
      });
    } else if (transcriptionResult.words && transcriptionResult.words.length > 0) {
      // 使用词级别的结果，按时间分组为句子
      const words = transcriptionResult.words;
      let currentSentence = '';
      let sentenceStart = words[0].startTime;
      let sentenceId = 1;

      for (let i = 0; i < words.length; i++) {
        const word = words[i];
        currentSentence += word.text;

        // 检查是否应该结束当前句子
        const shouldEndSentence = 
          i === words.length - 1 || // 最后一个词
          word.text.match(/[。！？.!?]/) || // 包含句号
          (i < words.length - 1 && words[i + 1].startTime - word.endTime > 1000); // 间隔超过1秒

        if (shouldEndSentence) {
          subtitles.push({
            id: sentenceId++,
            text: currentSentence.trim(),
            startTime: sentenceStart,
            endTime: word.endTime,
          });

          currentSentence = '';
          if (i < words.length - 1) {
            sentenceStart = words[i + 1].startTime;
          }
        }
      }
    } else {
      // 使用整体文本，按时间分割
      const text = transcriptionResult.text || '';
      const duration = transcriptionResult.duration || 60;
      const sentences = text.split(/[。！？.!?]/).filter(s => s.trim());
      
      const timePerSentence = duration / sentences.length;
      
      sentences.forEach((sentence, index) => {
        subtitles.push({
          id: index + 1,
          text: sentence.trim(),
          startTime: index * timePerSentence * 1000,
          endTime: (index + 1) * timePerSentence * 1000,
        });
      });
    }

    return subtitles;
  }

  /**
   * 渲染字幕视频
   * @param {Array} subtitles - 字幕数组
   * @param {Object} videoInfo - 视频信息
   * @returns {Promise<string>} 字幕视频路径
   */
  async renderSubtitleVideo(subtitles, videoInfo) {
    try {
      const subtitleVideoFileName = `${uuidv4()}_subtitles.mp4`;
      const subtitleVideoPath = path.join(config.paths.outputDir, subtitleVideoFileName);
      this.tempFiles.push(subtitleVideoPath);

      // 确保有视频信息才能渲染字幕
      if (!videoInfo.video) {
        throw new Error('Cannot render subtitles for audio-only file');
      }

      await renderSubtitles(subtitles, subtitleVideoPath, {
        duration: videoInfo.duration,
        fps: 30,
        videoSize: {
          width: videoInfo.video.width,
          height: videoInfo.video.height,
        },
        theme: 'default',
        size: 'medium',
        position: 'bottom',
        animation: 'fadeInUp',
      });

      logger.info('Subtitle video rendering completed', {
        dbId: this.dbId,
        subtitleVideoPath,
        subtitleCount: subtitles.length,
      });

      return subtitleVideoPath;

    } catch (error) {
      logger.error('Subtitle video rendering failed', {
        dbId: this.dbId,
        error: error.message,
      });
      throw new Error(`Subtitle video rendering failed: ${error.message}`);
    }
  }

  /**
   * 合成视频和音频
   * @param {string} subtitleVideoPath - 字幕视频路径
   * @param {string} audioPath - 音频路径
   * @returns {Promise<string>} 最终视频路径
   */
  async mergeVideoWithAudio(subtitleVideoPath, audioPath) {
    try {
      const finalVideoFileName = `${uuidv4()}_final.mp4`;
      const finalVideoPath = path.join(config.paths.outputDir, finalVideoFileName);

      await mergeVideoAudio(this.filePath, audioPath, finalVideoPath, {
        videoCodec: 'libx264',
        audioCodec: 'aac',
        preset: 'medium',
        crf: 23,
      });

      logger.info('Video audio merge completed', {
        dbId: this.dbId,
        finalVideoPath,
      });

      return finalVideoPath;

    } catch (error) {
      logger.error('Video audio merge failed', {
        dbId: this.dbId,
        error: error.message,
      });
      throw new Error(`Video audio merge failed: ${error.message}`);
    }
  }

  /**
   * 完成视频处理
   * @param {string} finalVideoPath - 最终视频路径
   * @returns {Promise<string>} 视频URL
   */
  async finalizeVideo(finalVideoPath) {
    try {
      // 生成最终文件名
      const timestamp = Date.now();
      const ext = path.extname(this.originalName);
      const baseName = path.basename(this.originalName, ext);
      const finalFileName = `${baseName}_${timestamp}_subtitled${ext}`;
      const publicVideoPath = path.join(config.paths.outputDir, finalFileName);

      // 移动文件到最终位置
      await fs.rename(finalVideoPath, publicVideoPath);

      // 生成缩略图
      try {
        const thumbnailPath = path.join(config.paths.outputDir, `${baseName}_${timestamp}_thumb.png`);
        await generateThumbnail(publicVideoPath, thumbnailPath);
      } catch (thumbError) {
        logger.warn('Failed to generate thumbnail', {
          dbId: this.dbId,
          error: thumbError.message,
        });
      }

      const videoUrl = `/downloads/${finalFileName}`;

      logger.info('Video processing finalized', {
        dbId: this.dbId,
        finalFileName,
        videoUrl,
      });

      return videoUrl;

    } catch (error) {
      logger.error('Video finalization failed', {
        dbId: this.dbId,
        error: error.message,
      });
      throw new Error(`Video finalization failed: ${error.message}`);
    }
  }

  /**
   * 更新处理进度
   * @param {string} stepName - 步骤名称
   */
  async updateProgress(stepName) {
    const step = this.processingSteps.find(s => s.name === stepName);
    if (step) {
      await this.updateJobStatus('PROCESSING', step.progress, step.description);
    }
  }

  /**
   * 更新任务状态
   * @param {string} status - 状态
   * @param {number} progress - 进度
   * @param {string} message - 消息
   * @param {Object} additionalData - 额外数据
   */
  async updateJobStatus(status, progress, message, additionalData = {}) {
    try {
      const updateData = {
        status,
        progress,
        updatedAt: new Date(),
      };

      if (status === 'PROCESSING' && progress === 0) {
        updateData.startedAt = new Date();
      }

      if (status === 'COMPLETED' || status === 'FAILED') {
        updateData.completedAt = new Date();
      }

      // 添加额外数据
      Object.assign(updateData, additionalData);

      await prisma.videoProcessingJob.update({
        where: { id: this.dbId },
        data: updateData,
      });

      logger.debug('Job status updated', {
        dbId: this.dbId,
        status,
        progress,
        message,
      });

    } catch (error) {
      logger.error('Failed to update job status', {
        dbId: this.dbId,
        error: error.message,
      });
    }
  }

  /**
   * 清理TOS文件
   */
  async cleanupTosFiles() {
    if (this.tosFiles.length > 0) {
      logger.info('☁️ Starting TOS files cleanup', {
        step: 'TOS-CLEANUP-START',
        dbId: this.dbId,
        tosFilesCount: this.tosFiles.length,
        tosFiles: this.tosFiles.map(f => ({ key: f.key, url: f.url, size: f.size }))
      });

      // 并行清理所有TOS文件
      const cleanupPromises = this.tosFiles.map(async (tosFile) => {
        try {
          await cleanupTosAudio(tosFile.key);
          return { success: true, key: tosFile.key };
        } catch (error) {
          logger.warn('⚠️ Failed to cleanup TOS file', {
            key: tosFile.key,
            error: error.message
          });
          return { success: false, key: tosFile.key, error: error.message };
        }
      });

      const results = await Promise.allSettled(cleanupPromises);
      const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
      const failed = results.length - successful;

      logger.info('✅ TOS files cleanup completed', {
        step: 'TOS-CLEANUP-COMPLETE',
        dbId: this.dbId,
        totalFiles: this.tosFiles.length,
        successful,
        failed
      });

      this.tosFiles = [];
    } else {
      logger.debug('☁️ No TOS files to cleanup', {
        step: 'TOS-CLEANUP-SKIP',
        dbId: this.dbId
      });
    }
  }

  /**
   * 清理音频文件
   */
  async cleanupAudioFiles() {
    if (this.audioFiles.length > 0) {
      logger.info('🎵 Starting audio files cleanup', {
        step: 'AUDIO-CLEANUP-START',
        dbId: this.dbId,
        audioFilesCount: this.audioFiles.length,
        audioFiles: this.audioFiles.map(f => require('path').resolve(f)),
        asrCompleted: this.asrCompleted
      });

      await cleanupFiles(this.audioFiles);

      logger.info('✅ Audio files cleanup completed', {
        step: 'AUDIO-CLEANUP-COMPLETE',
        dbId: this.dbId,
        cleanedFiles: this.audioFiles.length
      });

      this.audioFiles = [];
    } else {
      logger.debug('🎵 No audio files to cleanup', {
        step: 'AUDIO-CLEANUP-SKIP',
        dbId: this.dbId
      });
    }
  }

  /**
   * 清理临时文件
   */
  async cleanupTempFiles() {
    // 如果ASR已完成，清理音频文件和TOS文件
    if (this.asrCompleted) {
      await this.cleanupAudioFiles();
      await this.cleanupTosFiles();
    } else if (this.audioFiles.length > 0 || this.tosFiles.length > 0) {
      logger.warn('⚠️ ASR not completed, preserving audio and TOS files', {
        step: 'CLEANUP-PRESERVE-FILES',
        dbId: this.dbId,
        audioFilesCount: this.audioFiles.length,
        tosFilesCount: this.tosFiles.length,
        audioFiles: this.audioFiles.map(f => require('path').resolve(f)),
        tosFiles: this.tosFiles.map(f => ({ key: f.key, url: f.url })),
        note: 'Audio and TOS files will be cleaned up after ASR completion or failure'
      });
    }

    if (this.tempFiles.length > 0) {
      logger.info('🧹 Starting temporary files cleanup', {
        step: 'CLEANUP-START',
        dbId: this.dbId,
        tempFilesCount: this.tempFiles.length,
        tempFiles: this.tempFiles.map(f => require('path').resolve(f))
      });

      await cleanupFiles(this.tempFiles);

      logger.info('✅ Temporary files cleanup completed', {
        step: 'CLEANUP-COMPLETE',
        dbId: this.dbId,
        cleanedFiles: this.tempFiles.length
      });

      this.tempFiles = [];
    } else {
      logger.debug('📁 No temporary files to cleanup', {
        step: 'CLEANUP-SKIP',
        dbId: this.dbId
      });
    }
  }
}

/**
 * 处理视频任务（入口函数）
 * @param {Object} jobData - 任务数据
 * @returns {Promise<Object>} 处理结果
 */
async function processVideo(jobData) {
  const processor = new VideoProcessor(jobData);
  return await processor.process();
}

module.exports = {
  VideoProcessor,
  processVideo,
};
