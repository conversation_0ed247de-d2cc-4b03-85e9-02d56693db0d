/**
 * FFmpeg 音视频处理模块
 * 提供音频提取、视频合成、格式转换等功能
 */

const ffmpeg = require('fluent-ffmpeg');
const path = require('path');
const fs = require('fs').promises;
const { createModuleLogger } = require('../common/logger');
const config = require('../common/config');

const logger = createModuleLogger('ffmpegProcessor');

// 设置 FFmpeg 路径
ffmpeg.setFfmpegPath(config.ffmpeg.path);
ffmpeg.setFfprobePath(config.ffmpeg.probePath);

/**
 * 获取视频信息
 * @param {string} videoPath - 视频文件路径
 * @returns {Promise<Object>} 视频信息
 */
async function getVideoInfo(videoPath) {
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(videoPath, (err, metadata) => {
      if (err) {
        logger.error('Failed to get video info', {
          videoPath,
          error: err.message,
        });
        reject(err);
        return;
      }

      const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
      const audioStream = metadata.streams.find(stream => stream.codec_type === 'audio');

      const info = {
        duration: parseFloat(metadata.format.duration) || 0,
        size: parseInt(metadata.format.size) || 0,
        bitrate: parseInt(metadata.format.bit_rate) || 0,
        format: metadata.format.format_name,
        video: videoStream ? {
          codec: videoStream.codec_name,
          width: videoStream.width,
          height: videoStream.height,
          fps: eval(videoStream.r_frame_rate) || 0,
          bitrate: parseInt(videoStream.bit_rate) || 0,
        } : null,
        audio: audioStream ? {
          codec: audioStream.codec_name,
          sampleRate: parseInt(audioStream.sample_rate) || 0,
          channels: audioStream.channels,
          bitrate: parseInt(audioStream.bit_rate) || 0,
        } : null,
      };

      logger.debug('Video info extracted', {
        videoPath,
        duration: info.duration,
        format: info.format,
        hasVideo: !!info.video,
        hasAudio: !!info.audio,
      });

      resolve(info);
    });
  });
}

/**
 * 从视频中提取音频
 * @param {string} videoPath - 视频文件路径
 * @param {string} outputPath - 输出音频文件路径
 * @param {Object} options - 提取选项
 * @returns {Promise<string>} 输出文件路径
 */
async function extractAudio(videoPath, outputPath, options = {}) {
  const {
    format = 'wav',
    sampleRate = 16000,
    channels = 1,
    bitrate = '128k',
  } = options;

  return new Promise((resolve, reject) => {
    logger.info('Starting audio extraction', {
      videoPath,
      outputPath,
      format,
      sampleRate,
      channels,
    });

    const command = ffmpeg(videoPath)
      .noVideo()
      .audioCodec('pcm_s16le')
      .audioFrequency(sampleRate)
      .audioChannels(channels)
      .audioBitrate(bitrate)
      .format(format)
      .on('start', (commandLine) => {
        logger.debug('FFmpeg command started', { commandLine });
      })
      .on('progress', (progress) => {
        logger.debug('Audio extraction progress', {
          percent: progress.percent,
          timemark: progress.timemark,
        });
      })
      .on('end', () => {
        logger.info('Audio extraction completed', {
          videoPath,
          outputPath,
        });
        resolve(outputPath);
      })
      .on('error', (err) => {
        // 检查是否是由于SIGINT（Ctrl+C）导致的错误
        if (err.message.includes('signal 2') || err.message.includes('SIGINT')) {
          const cancellationError = new Error('Audio extraction cancelled due to shutdown signal.');
          logger.warn('Audio extraction process was cancelled by user.', {
            videoPath,
            outputPath,
            error: cancellationError.message,
          });
          reject(cancellationError);
        } else {
          logger.error('Audio extraction failed', {
            videoPath,
            outputPath,
            error: err.message,
          });
          reject(err);
        }
      });

    command.save(outputPath);
  });
}

/**
 * 合成视频和音频
 * @param {string} videoPath - 视频文件路径
 * @param {string} audioPath - 音频文件路径
 * @param {string} outputPath - 输出文件路径
 * @param {Object} options - 合成选项
 * @returns {Promise<string>} 输出文件路径
 */
async function mergeVideoAudio(videoPath, audioPath, outputPath, options = {}) {
  const {
    videoCodec = 'libx264',
    audioCodec = 'aac',
    preset = 'medium',
    crf = 23,
  } = options;

  return new Promise((resolve, reject) => {
    logger.info('Starting video audio merge', {
      videoPath,
      audioPath,
      outputPath,
    });

    const command = ffmpeg()
      .input(videoPath)
      .input(audioPath)
      .videoCodec(videoCodec)
      .audioCodec(audioCodec)
      .outputOptions([
        `-preset ${preset}`,
        `-crf ${crf}`,
        '-map 0:v:0',
        '-map 1:a:0',
        '-shortest',
      ])
      .on('start', (commandLine) => {
        logger.debug('FFmpeg merge command started', { commandLine });
      })
      .on('progress', (progress) => {
        logger.debug('Video merge progress', {
          percent: progress.percent,
          timemark: progress.timemark,
        });
      })
      .on('end', () => {
        logger.info('Video audio merge completed', {
          videoPath,
          audioPath,
          outputPath,
        });
        resolve(outputPath);
      })
      .on('error', (err) => {
        logger.error('Video audio merge failed', {
          videoPath,
          audioPath,
          outputPath,
          error: err.message,
        });
        reject(err);
      });

    command.save(outputPath);
  });
}

/**
 * 转换视频格式
 * @param {string} inputPath - 输入文件路径
 * @param {string} outputPath - 输出文件路径
 * @param {Object} options - 转换选项
 * @returns {Promise<string>} 输出文件路径
 */
async function convertVideo(inputPath, outputPath, options = {}) {
  const {
    format = 'mp4',
    videoCodec = 'libx264',
    audioCodec = 'aac',
    preset = 'medium',
    crf = 23,
    scale = null,
  } = options;

  return new Promise((resolve, reject) => {
    logger.info('Starting video conversion', {
      inputPath,
      outputPath,
      format,
      videoCodec,
      audioCodec,
    });

    const command = ffmpeg(inputPath)
      .videoCodec(videoCodec)
      .audioCodec(audioCodec)
      .format(format)
      .outputOptions([
        `-preset ${preset}`,
        `-crf ${crf}`,
      ]);

    // 添加缩放选项
    if (scale) {
      command.size(scale);
    }

    command
      .on('start', (commandLine) => {
        logger.debug('FFmpeg conversion command started', { commandLine });
      })
      .on('progress', (progress) => {
        logger.debug('Video conversion progress', {
          percent: progress.percent,
          timemark: progress.timemark,
        });
      })
      .on('end', () => {
        logger.info('Video conversion completed', {
          inputPath,
          outputPath,
        });
        resolve(outputPath);
      })
      .on('error', (err) => {
        logger.error('Video conversion failed', {
          inputPath,
          outputPath,
          error: err.message,
        });
        reject(err);
      });

    command.save(outputPath);
  });
}

/**
 * 生成视频缩略图
 * @param {string} videoPath - 视频文件路径
 * @param {string} outputPath - 输出图片路径
 * @param {Object} options - 缩略图选项
 * @returns {Promise<string>} 输出文件路径
 */
async function generateThumbnail(videoPath, outputPath, options = {}) {
  const {
    timemarks = ['50%'],
    size = '320x240',
    format = 'png',
  } = options;

  return new Promise((resolve, reject) => {
    logger.info('Starting thumbnail generation', {
      videoPath,
      outputPath,
      timemarks,
      size,
    });

    ffmpeg(videoPath)
      .screenshots({
        timemarks,
        size,
        filename: path.basename(outputPath),
        folder: path.dirname(outputPath),
      })
      .on('end', () => {
        logger.info('Thumbnail generation completed', {
          videoPath,
          outputPath,
        });
        resolve(outputPath);
      })
      .on('error', (err) => {
        logger.error('Thumbnail generation failed', {
          videoPath,
          outputPath,
          error: err.message,
        });
        reject(err);
      });
  });
}

/**
 * 验证音频文件格式（用于ASR）
 * @param {string} audioPath - 音频文件路径
 * @returns {Promise<boolean>} 是否为有效格式
 */
async function validateAudioFormat(audioPath) {
  try {
    const info = await getVideoInfo(audioPath);
    
    // 检查是否有音频流
    if (!info.audio) {
      return false;
    }

    // 检查音频格式是否符合ASR要求
    const validFormats = ['wav', 'mp3', 'ogg'];
    const validSampleRates = [8000, 16000, 22050, 44100, 48000];
    
    // 这里简化检查，实际可以更详细
    return info.duration > 0 && info.audio.sampleRate > 0;
  } catch (error) {
    logger.error('Audio format validation failed', {
      audioPath,
      error: error.message,
    });
    return false;
  }
}

/**
 * 清理临时文件
 * @param {string[]} filePaths - 要删除的文件路径数组
 * @returns {Promise<void>}
 */
async function cleanupFiles(filePaths) {
  const results = await Promise.allSettled(
    filePaths.map(async (filePath) => {
      try {
        await fs.unlink(filePath);
        logger.debug('File cleaned up', { filePath });
      } catch (error) {
        if (error.code !== 'ENOENT') {
          logger.warn('Failed to cleanup file', {
            filePath,
            error: error.message,
          });
        }
      }
    })
  );

  const failed = results.filter(result => result.status === 'rejected');
  if (failed.length > 0) {
    logger.warn('Some files failed to cleanup', {
      failedCount: failed.length,
      totalCount: filePaths.length,
    });
  } else {
    logger.info('All temporary files cleaned up', {
      count: filePaths.length,
    });
  }
}

module.exports = {
  getVideoInfo,
  extractAudio,
  mergeVideoAudio,
  convertVideo,
  generateThumbnail,
  validateAudioFormat,
  cleanupFiles,
};
