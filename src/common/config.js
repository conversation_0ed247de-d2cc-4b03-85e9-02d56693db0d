/**
 * 应用配置模块
 * 统一管理所有配置参数，支持环境变量验证
 */

const path = require('path');
require('dotenv').config();

/**
 * 验证必需的环境变量
 * @param {string} key - 环境变量名
 * @param {*} defaultValue - 默认值
 * @returns {*} 环境变量值或默认值
 */
function getEnvVar(key, defaultValue = undefined) {
  const value = process.env[key];
  if (value === undefined && defaultValue === undefined) {
    throw new Error(`Missing required environment variable: ${key}`);
  }
  return value || defaultValue;
}

/**
 * 转换字符串为布尔值
 * @param {string} value - 字符串值
 * @returns {boolean} 布尔值
 */
function parseBoolean(value) {
  return value === 'true' || value === '1';
}

/**
 * 转换字符串为数字
 * @param {string} value - 字符串值
 * @param {number} defaultValue - 默认值
 * @returns {number} 数字值
 */
function parseNumber(value, defaultValue) {
  const num = parseInt(value, 10);
  return isNaN(num) ? defaultValue : num;
}

// 应用配置
const config = {
  // 应用环境配置
  app: {
    env: getEnvVar('NODE_ENV', 'development'),
    port: parseNumber(getEnvVar('API_PORT', '3100'), 3100),
    isDevelopment: getEnvVar('NODE_ENV', 'development') === 'development',
    isProduction: getEnvVar('NODE_ENV', 'development') === 'production',
  },

  // 数据库配置
  database: {
    url: getEnvVar('DATABASE_URL'),
  },

  // Redis 配置
  redis: {
    host: getEnvVar('REDIS_HOST', 'localhost'),
    port: parseNumber(getEnvVar('REDIS_PORT', '6379'), 6379),
    password: getEnvVar('REDIS_PASSWORD', ''),
    db: parseNumber(getEnvVar('REDIS_DB', '0'), 0),
  },

  // 火山引擎 ASR API 配置
  volcengine: {
    appId: getEnvVar('VOLC_APP_ID'),
    accessKeyId: getEnvVar('VOLC_ACCESS_KEY_ID'),
    secretAccessKey: getEnvVar('VOLC_ACCESS_KEY_SECRET'),
    apiKey: getEnvVar('VOLC_API_KEY'),
    region: getEnvVar('VOLC_REGION', 'cn-north-1'),
    endpoint: 'https://openspeech.bytedance.com',
  },

  // 文件路径配置
  paths: {
    uploadDir: path.resolve(getEnvVar('UPLOAD_DIR', './src/public/uploads')),
    outputDir: path.resolve(getEnvVar('OUTPUT_DIR', './src/public/outputs')),
    renderTemplateDir: path.resolve(getEnvVar('RENDER_TEMPLATE_DIR', './src/public/render-template')),
    logDir: path.resolve(getEnvVar('LOG_FILE_PATH', './logs')),
  },

  // 文件上传配置
  upload: {
    maxFileSize: parseNumber(getEnvVar('MAX_FILE_SIZE', '500'), 500) * 1024 * 1024, // 转换为字节
    allowedMimeTypes: [
      'video/mp4',
      'video/avi',
      'video/mov',
      'video/wmv',
      'video/flv',
      'video/webm',
      'video/mkv'
    ],
  },

  // 任务队列配置
  queue: {
    name: 'video-processing',
    concurrency: parseNumber(getEnvVar('QUEUE_CONCURRENCY', '2'), 2),
    maxAttempts: parseNumber(getEnvVar('QUEUE_MAX_ATTEMPTS', '3'), 3),
    delayRetry: parseNumber(getEnvVar('QUEUE_DELAY_RETRY', '5000'), 5000),
    removeOnComplete: 10,
    removeOnFail: 50,
  },

  // 日志配置
  logging: {
    level: getEnvVar('LOG_LEVEL', 'debug'), // 临时设置为debug级别以便调试ASR问题
    maxSize: getEnvVar('LOG_MAX_SIZE', '20m'),
    maxFiles: getEnvVar('LOG_MAX_FILES', '14d'),
    datePattern: 'YYYY-MM-DD',
  },

  // FFmpeg 配置
  ffmpeg: {
    path: getEnvVar('FFMPEG_PATH', 'ffmpeg'),
    probePath: getEnvVar('FFPROBE_PATH', 'ffprobe'),
    timeout: 300000, // 5分钟超时
  },

  // Puppeteer 配置
  puppeteer: {
    headless: parseBoolean(getEnvVar('PUPPETEER_HEADLESS', 'true')),
    timeout: parseNumber(getEnvVar('PUPPETEER_TIMEOUT', '30000'), 30000),
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--disable-gpu'
    ],
  },

  // 安全配置
  security: {
    jwtSecret: getEnvVar('JWT_SECRET'),
    corsOrigin: getEnvVar('CORS_ORIGIN', 'http://localhost:3100'),
  },

  // 监控配置
  monitoring: {
    enabled: parseBoolean(getEnvVar('ENABLE_METRICS', 'true')),
    port: parseNumber(getEnvVar('METRICS_PORT', '9090'), 9090),
  },
};

// 验证配置
function validateConfig() {
  const requiredFields = [
    'database.url',
    'volcengine.appId',
    'volcengine.accessKeyId',
    'volcengine.secretAccessKey',
    'volcengine.apiKey',
    'security.jwtSecret',
  ];

  for (const field of requiredFields) {
    const keys = field.split('.');
    let value = config;
    for (const key of keys) {
      value = value[key];
    }
    if (!value) {
      throw new Error(`Missing required configuration: ${field}`);
    }
  }
}

// 在生产环境中验证配置
if (config.app.isProduction) {
  validateConfig();
}

module.exports = config;