/**
 * TOS (Volcengine Object Storage) 服务封装
 * 提供文件上传、URL生成等功能
 */

const { TosClient } = require('@volcengine/tos-sdk');
const fs = require('fs').promises;
const path = require('path');
const { createModuleLogger } = require('./logger');
const config = require('./config');

const logger = createModuleLogger('tosService');

/**
 * TOS 服务类
 */
class TosService {
  constructor() {
    // 从环境变量获取TOS配置
    const rawEndpoint = process.env.TOS_ENDPOINT || 'tos-cn-shanghai.volces.com';
    const cleanEndpoint = rawEndpoint.replace(/^https?:\/\//, ''); // 移除协议头
    this.config = {
      region: process.env.TOS_REGION || 'cn-shanghai',
      endpoint: `https://${cleanEndpoint}`, // 强制使用 https
      accessKeyId: process.env.VOLC_ACCESS_KEY_ID,
      accessKeySecret: process.env.VOLC_ACCESS_KEY_SECRET,
      bucket: process.env.TOS_BUCKET || 'argochainhub',
      requestTimeout: parseInt(process.env.TOS_REQUEST_TIMEOUT) || 60000,
    };

    // 验证必需的配置
    if (!this.config.accessKeyId || !this.config.accessKeySecret) {
      throw new Error('TOS credentials not configured. Please set VOLC_ACCESS_KEY_ID and VOLC_ACCESS_KEY_SECRET');
    }

    // 初始化TOS客户端 - SDK需要不包含协议的endpoint
    const sdkEndpoint = this.config.endpoint.replace(/^https?:\/\//, '');

    // 强制Node.js使用HTTPS
    process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0'; // 临时禁用TLS验证以排除证书问题

    // TOS SDK配置 - 尝试多种配置方式
    const tosConfig = {
      accessKeyId: this.config.accessKeyId,
      accessKeySecret: this.config.accessKeySecret,
      region: this.config.region,
      endpoint: sdkEndpoint,
      requestTimeout: this.config.requestTimeout,
      // 尝试强制HTTPS的各种配置
      secure: true,
      protocol: 'https:',
      port: 443,
    };

    logger.info('TOS client configuration', {
      region: tosConfig.region,
      endpoint: tosConfig.endpoint,
      fullEndpointForReference: `https://${sdkEndpoint}`,
      hasAccessKey: !!tosConfig.accessKeyId,
      hasSecretKey: !!tosConfig.accessKeySecret,
    });

    this.tosClient = new TosClient(tosConfig);

    logger.info('TOS service initialized', {
      region: this.config.region,
      endpoint: this.config.endpoint,
      bucket: this.config.bucket,
      requestTimeout: this.config.requestTimeout,
    });
  }

  /**
   * 从文件路径上传文件到TOS
   * @param {string} key - TOS对象键
   * @param {string} filePath - 本地文件路径
   * @param {string} contentType - 文件MIME类型
   * @param {Object} metadata - 自定义元数据
   * @returns {Promise<Object>} 上传结果
   */
  async uploadFileFromPath(key, filePath, contentType, metadata = {}) {
    try {
      // 检查文件是否存在
      const fileStats = await fs.stat(filePath);
      const fileSize = fileStats.size;

      logger.info('Starting TOS upload from file path', {
        key,
        filePath: path.resolve(filePath),
        contentType,
        fileSize,
        fileSizeMB: (fileSize / 1024 / 1024).toFixed(2),
        metadata,
      });

      // 使用TOS SDK的putObjectFromFile方法
      const response = await this.tosClient.putObjectFromFile({
        bucket: this.config.bucket,
        key: key,
        filePath: filePath,
        contentType: contentType,
        meta: metadata,
      });

      const url = this.getFileUrl(key);

      const result = {
        key,
        url,
        bucket: this.config.bucket,
        etag: response.headers?.etag || '',
        size: fileSize,
        contentType,
        metadata,
      };

      logger.info('TOS upload completed successfully', {
        key,
        url,
        bucket: this.config.bucket,
        etag: result.etag,
        size: fileSize,
        fileSizeMB: (fileSize / 1024 / 1024).toFixed(2),
      });

      return result;

    } catch (error) {
      logger.error('TOS upload failed', {
        key,
        filePath,
        error: error.message,
        errorCode: error.code,
        statusCode: error.statusCode,
      });
      throw new Error(`TOS upload failed: ${error.message}`);
    }
  }

  /**
   * 从Buffer上传文件到TOS
   * @param {string} key - TOS对象键
   * @param {Buffer} buffer - 文件数据
   * @param {string} contentType - 文件MIME类型
   * @param {Object} metadata - 自定义元数据
   * @returns {Promise<Object>} 上传结果
   */
  async uploadBuffer(key, buffer, contentType, metadata = {}) {
    try {
      logger.info('Starting TOS upload from buffer', {
        key,
        contentType,
        bufferSize: buffer.length,
        bufferSizeMB: (buffer.length / 1024 / 1024).toFixed(2),
        metadata,
      });

      const response = await this.tosClient.putObject({
        bucket: this.config.bucket,
        key: key,
        body: buffer,
        contentType: contentType,
        meta: metadata,
      });

      const url = this.getFileUrl(key);

      const result = {
        key,
        url,
        bucket: this.config.bucket,
        etag: response.headers?.etag || '',
        size: buffer.length,
        contentType,
        metadata,
      };

      logger.info('TOS buffer upload completed successfully', {
        key,
        url,
        bucket: this.config.bucket,
        etag: result.etag,
        size: buffer.length,
        bufferSizeMB: (buffer.length / 1024 / 1024).toFixed(2),
      });

      return result;

    } catch (error) {
      logger.error('TOS buffer upload failed', {
        key,
        error: error.message,
        errorCode: error.code,
        statusCode: error.statusCode,
      });
      throw new Error(`TOS buffer upload failed: ${error.message}`);
    }
  }

  /**
   * 生成文件的公共URL
   * @param {string} key - TOS对象键
   * @returns {string} 公共URL
   */
  getFileUrl(key) {
    // 构建标准的TOS公共URL
    const baseEndpoint = this.config.endpoint.replace(/^https?:\/\//, '');
    return `https://${this.config.bucket}.${baseEndpoint}/${key}`;
  }

  /**
   * 生成预签名URL
   * @param {string} key - TOS对象键
   * @param {number} expiresIn - 过期时间（秒）
   * @returns {string} 预签名URL
   */
  generateSignedUrl(key, expiresIn = 3600) {
    try {
      const url = this.tosClient.getPreSignedUrl({
        bucket: this.config.bucket,
        key: key,
        expires: expiresIn,
      });

      logger.debug('Generated signed URL', {
        key,
        expiresIn,
        url: url.substring(0, 100) + '...',
      });

      return url;
    } catch (error) {
      logger.error('Failed to generate signed URL', {
        key,
        error: error.message,
      });
      // 降级到普通URL
      return this.getFileUrl(key);
    }
  }

  /**
   * 删除TOS对象
   * @param {string} key - TOS对象键
   * @returns {Promise<void>}
   */
  async deleteFile(key) {
    try {
      await this.tosClient.deleteObject({
        bucket: this.config.bucket,
        key: key,
      });

      logger.info('TOS file deleted successfully', { key });
    } catch (error) {
      logger.error('TOS file deletion failed', {
        key,
        error: error.message,
        errorCode: error.code,
      });
      throw new Error(`TOS file deletion failed: ${error.message}`);
    }
  }

  /**
   * 检查文件是否存在
   * @param {string} key - TOS对象键
   * @returns {Promise<boolean>} 文件是否存在
   */
  async fileExists(key) {
    try {
      await this.tosClient.headObject({
        bucket: this.config.bucket,
        key: key,
      });
      return true;
    } catch (error) {
      if (error.statusCode === 404) {
        return false;
      }
      throw error;
    }
  }

  /**
   * 生成用于ASR的音频文件键名
   * @param {string} originalName - 原始文件名
   * @param {string} dbId - 数据库ID
   * @returns {string} TOS对象键
   */
  generateAudioKey(originalName, dbId) {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    const ext = path.extname(originalName) || '.wav';
    
    return `audio/asr/${dbId}/${timestamp}-${random}${ext}`;
  }
}

// 创建单例实例
let tosServiceInstance = null;

/**
 * 获取TOS服务实例
 * @returns {TosService} TOS服务实例
 */
function getTosService() {
  if (!tosServiceInstance) {
    tosServiceInstance = new TosService();
  }
  return tosServiceInstance;
}

module.exports = {
  TosService,
  getTosService,
};
