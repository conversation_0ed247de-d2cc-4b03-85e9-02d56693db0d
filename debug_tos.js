/**
 * Debug TOS configuration and SDK behavior
 */

require('dotenv').config();
const { TosClient } = require('@volcengine/tos-sdk');

console.log('Environment variables:');
console.log('TOS_REGION:', process.env.TOS_REGION);
console.log('TOS_ENDPOINT:', process.env.TOS_ENDPOINT);
console.log('TOS_BUCKET:', process.env.TOS_BUCKET);
console.log('VOLC_ACCESS_KEY_ID:', process.env.VOLC_ACCESS_KEY_ID ? 'SET' : 'NOT SET');
console.log('VOLC_ACCESS_KEY_SECRET:', process.env.VOLC_ACCESS_KEY_SECRET ? 'SET' : 'NOT SET');

const rawEndpoint = process.env.TOS_ENDPOINT || 'tos-cn-shanghai.volces.com';
const cleanEndpoint = rawEndpoint.replace(/^https?:\/\//, '');

console.log('\nEndpoint processing:');
console.log('Raw endpoint:', rawEndpoint);
console.log('Clean endpoint:', cleanEndpoint);

const config = {
  region: process.env.TOS_REGION || 'cn-shanghai',
  endpoint: cleanEndpoint,
  accessKeyId: process.env.VOLC_ACCESS_KEY_ID,
  accessKeySecret: process.env.VOLC_ACCESS_KEY_SECRET,
  bucket: process.env.TOS_BUCKET || 'argochainhub',
  requestTimeout: parseInt(process.env.TOS_REQUEST_TIMEOUT) || 60000,
};

console.log('\nTOS Client config:');
console.log(JSON.stringify(config, null, 2));

try {
  const tosClient = new TosClient({
    accessKeyId: config.accessKeyId,
    accessKeySecret: config.accessKeySecret,
    region: config.region,
    endpoint: config.endpoint,
    requestTimeout: config.requestTimeout,
  });

  console.log('\n✅ TOS Client created successfully');
  
  // Try to generate a pre-signed URL to see what URL it creates
  try {
    const testKey = 'test/debug-test.txt';
    const signedUrl = tosClient.getPreSignedUrl({
      bucket: config.bucket,
      key: testKey,
      expires: 3600,
    });
    
    console.log('\nGenerated signed URL:', signedUrl);
    console.log('URL protocol:', signedUrl.split('://')[0]);
    
  } catch (urlError) {
    console.error('\n❌ Failed to generate signed URL:', urlError.message);
  }

} catch (error) {
  console.error('\n❌ Failed to create TOS client:', error.message);
  console.error('Error code:', error.code);
  console.error('Full error:', error);
}
