/**
 * ASR工作流测试脚本
 * 测试修复后的ASR工作流程，包括TOS集成和错误处理
 */

require('dotenv').config();
const path = require('path');
const fs = require('fs');
const { createModuleLogger } = require('./src/common/logger');
const { getTosService } = require('./src/common/tosService');
const { submitTranscriptionTask, cleanupTosAudio } = require('./src/worker/transcriber');

const logger = createModuleLogger('asrTest');

/**
 * 测试TOS服务连接
 */
async function testTosConnection() {
  try {
    logger.info('🧪 Testing TOS service connection...');
    
    const tosService = getTosService();
    
    // 创建一个测试文件
    const testContent = Buffer.from('This is a test file for TOS connection');
    const testKey = `test/connection-test-${Date.now()}.txt`;
    
    // 上传测试文件
    const uploadResult = await tosService.uploadBuffer(testKey, testContent, 'text/plain');
    logger.info('✅ TOS upload test successful', {
      key: uploadResult.key,
      url: uploadResult.url,
      size: uploadResult.size
    });
    
    // 验证文件存在
    const exists = await tosService.fileExists(testKey);
    logger.info('✅ TOS file existence check', { exists });
    
    // 清理测试文件
    await tosService.deleteFile(testKey);
    logger.info('✅ TOS cleanup test successful');
    
    return true;
  } catch (error) {
    logger.error('❌ TOS connection test failed', { error: error.message });
    return false;
  }
}

/**
 * 测试音频文件上传和ASR提交（不实际执行ASR）
 */
async function testAudioUploadWorkflow() {
  try {
    logger.info('🧪 Testing audio upload workflow...');
    
    // 创建一个模拟的WAV文件
    const testAudioPath = path.join(__dirname, 'test_audio.wav');
    const wavHeader = Buffer.alloc(44);
    // 简单的WAV文件头
    wavHeader.write('RIFF', 0);
    wavHeader.writeUInt32LE(36, 4);
    wavHeader.write('WAVE', 8);
    wavHeader.write('fmt ', 12);
    wavHeader.writeUInt32LE(16, 16);
    wavHeader.writeUInt16LE(1, 20); // PCM
    wavHeader.writeUInt16LE(1, 22); // 单声道
    wavHeader.writeUInt32LE(16000, 24); // 采样率
    wavHeader.writeUInt32LE(32000, 28); // 字节率
    wavHeader.writeUInt16LE(2, 32); // 块对齐
    wavHeader.writeUInt16LE(16, 34); // 位深度
    wavHeader.write('data', 36);
    wavHeader.writeUInt32LE(0, 40);
    
    fs.writeFileSync(testAudioPath, wavHeader);
    
    logger.info('📁 Created test audio file', {
      path: testAudioPath,
      size: fs.statSync(testAudioPath).size
    });
    
    // 测试音频上传和ASR提交（使用测试模式）
    const testDbId = `test_${Date.now()}`;
    
    try {
      // 这会上传到TOS并尝试提交ASR任务
      const result = await submitTranscriptionTask(testAudioPath, {
        enableItn: true,
        enablePunc: true,
        showUtterances: true,
        language: 'zh-CN',
      }, testDbId);
      
      logger.info('✅ Audio upload and ASR submission test', {
        taskId: result.taskId,
        tosKey: result.tosFile.key,
        tosUrl: result.tosFile.url,
        tosSize: result.tosFile.size
      });
      
      // 清理TOS文件
      await cleanupTosAudio(result.tosFile.key);
      logger.info('✅ TOS cleanup after test successful');
      
    } catch (error) {
      logger.warn('⚠️ ASR submission failed (expected in test environment)', {
        error: error.message,
        note: 'This is expected if ASR credentials are not properly configured'
      });
    }
    
    // 清理本地测试文件
    fs.unlinkSync(testAudioPath);
    logger.info('✅ Local test file cleanup successful');
    
    return true;
  } catch (error) {
    logger.error('❌ Audio upload workflow test failed', { error: error.message });
    return false;
  }
}

/**
 * 测试错误处理和恢复
 */
async function testErrorHandling() {
  try {
    logger.info('🧪 Testing error handling...');
    
    // 测试不存在的文件
    try {
      await submitTranscriptionTask('/nonexistent/file.wav', {}, 'test_error');
      logger.error('❌ Should have thrown error for nonexistent file');
      return false;
    } catch (error) {
      logger.info('✅ Correctly handled nonexistent file error', { error: error.message });
    }
    
    // 测试无效文件格式
    const invalidFilePath = path.join(__dirname, 'test_invalid.txt');
    fs.writeFileSync(invalidFilePath, 'This is not an audio file');
    
    try {
      await submitTranscriptionTask(invalidFilePath, {}, 'test_invalid');
      logger.error('❌ Should have thrown error for invalid file format');
      return false;
    } catch (error) {
      logger.info('✅ Correctly handled invalid file format error', { error: error.message });
    } finally {
      fs.unlinkSync(invalidFilePath);
    }
    
    return true;
  } catch (error) {
    logger.error('❌ Error handling test failed', { error: error.message });
    return false;
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  logger.info('🚀 Starting ASR workflow tests...');
  
  const tests = [
    { name: 'TOS Connection', fn: testTosConnection },
    { name: 'Audio Upload Workflow', fn: testAudioUploadWorkflow },
    { name: 'Error Handling', fn: testErrorHandling }
  ];
  
  const results = [];
  
  for (const test of tests) {
    logger.info(`\n📋 Running test: ${test.name}`);
    try {
      const result = await test.fn();
      results.push({ name: test.name, success: result });
      logger.info(`${result ? '✅' : '❌'} Test ${test.name}: ${result ? 'PASSED' : 'FAILED'}`);
    } catch (error) {
      results.push({ name: test.name, success: false, error: error.message });
      logger.error(`❌ Test ${test.name}: FAILED with error`, { error: error.message });
    }
  }
  
  // 汇总结果
  logger.info('\n📊 Test Results Summary:');
  const passed = results.filter(r => r.success).length;
  const failed = results.length - passed;
  
  results.forEach(result => {
    logger.info(`${result.success ? '✅' : '❌'} ${result.name}: ${result.success ? 'PASSED' : 'FAILED'}`);
    if (result.error) {
      logger.info(`   Error: ${result.error}`);
    }
  });
  
  logger.info(`\n🎯 Overall: ${passed}/${results.length} tests passed`);
  
  if (failed === 0) {
    logger.info('🎉 All tests passed! ASR workflow is ready.');
  } else {
    logger.warn(`⚠️ ${failed} test(s) failed. Please check the configuration and fix issues.`);
  }
  
  return failed === 0;
}

// 运行测试
if (require.main === module) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      logger.error('💥 Test runner crashed', { error: error.message });
      process.exit(1);
    });
}

module.exports = {
  testTosConnection,
  testAudioUploadWorkflow,
  testErrorHandling,
  runAllTests
};
