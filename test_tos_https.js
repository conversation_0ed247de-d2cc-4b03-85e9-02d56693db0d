/**
 * Test TOS HTTPS configuration specifically
 */

require('dotenv').config();
const { getTosService } = require('./src/common/tosService');
const { createModuleLogger } = require('./src/common/logger');

const logger = createModuleLogger('tosHttpsTest');

async function testTosHttpsConfiguration() {
  try {
    logger.info('🧪 Testing TOS HTTPS configuration...');
    
    const tosService = getTosService();
    
    // Create a small test buffer
    const testContent = Buffer.from('HTTPS test content');
    const testKey = `test/https-test-${Date.now()}.txt`;
    
    logger.info('📤 Attempting TOS upload with HTTPS configuration', {
      key: testKey,
      contentSize: testContent.length
    });
    
    // Try to upload
    const uploadResult = await tosService.uploadBuffer(testKey, testContent, 'text/plain');
    
    logger.info('✅ TOS HTTPS upload successful!', {
      key: uploadResult.key,
      url: uploadResult.url,
      size: uploadResult.size,
      etag: uploadResult.etag
    });
    
    // Verify the URL uses HTTPS
    if (uploadResult.url.startsWith('https://')) {
      logger.info('✅ Generated URL uses HTTPS protocol');
    } else {
      logger.error('❌ Generated URL does not use HTTPS', { url: uploadResult.url });
    }
    
    // Clean up
    await tosService.deleteFile(testKey);
    logger.info('✅ Test file cleaned up successfully');
    
    return true;
    
  } catch (error) {
    logger.error('❌ TOS HTTPS test failed', { 
      error: error.message,
      errorCode: error.code,
      stack: error.stack
    });
    return false;
  }
}

async function testTosEndpointConfiguration() {
  try {
    logger.info('🔍 Checking TOS endpoint configuration...');
    
    const tosService = getTosService();
    
    // Check internal configuration
    logger.info('TOS service configuration check', {
      region: process.env.TOS_REGION,
      endpoint: process.env.TOS_ENDPOINT,
      bucket: process.env.TOS_BUCKET,
      hasAccessKey: !!process.env.VOLC_ACCESS_KEY_ID,
      hasSecretKey: !!process.env.VOLC_ACCESS_KEY_SECRET
    });
    
    // Try to generate a signed URL to see what protocol it uses
    const testKey = 'test/protocol-check.txt';
    const signedUrl = tosService.generateSignedUrl(testKey, 3600);
    
    logger.info('Generated signed URL analysis', {
      url: signedUrl.substring(0, 100) + '...',
      protocol: signedUrl.split('://')[0],
      isHttps: signedUrl.startsWith('https://')
    });
    
    return signedUrl.startsWith('https://');
    
  } catch (error) {
    logger.error('❌ TOS endpoint configuration check failed', { 
      error: error.message 
    });
    return false;
  }
}

async function runHttpsTests() {
  logger.info('🚀 Starting TOS HTTPS configuration tests...');
  
  const tests = [
    { name: 'TOS Endpoint Configuration', fn: testTosEndpointConfiguration },
    { name: 'TOS HTTPS Upload', fn: testTosHttpsConfiguration }
  ];
  
  const results = [];
  
  for (const test of tests) {
    logger.info(`\n📋 Running test: ${test.name}`);
    try {
      const result = await test.fn();
      results.push({ name: test.name, success: result });
      logger.info(`${result ? '✅' : '❌'} Test ${test.name}: ${result ? 'PASSED' : 'FAILED'}`);
    } catch (error) {
      results.push({ name: test.name, success: false, error: error.message });
      logger.error(`❌ Test ${test.name}: FAILED with error`, { error: error.message });
    }
  }
  
  // Summary
  logger.info('\n📊 HTTPS Test Results Summary:');
  const passed = results.filter(r => r.success).length;
  const failed = results.length - passed;
  
  results.forEach(result => {
    logger.info(`${result.success ? '✅' : '❌'} ${result.name}: ${result.success ? 'PASSED' : 'FAILED'}`);
    if (result.error) {
      logger.info(`   Error: ${result.error}`);
    }
  });
  
  logger.info(`\n🎯 Overall: ${passed}/${results.length} tests passed`);
  
  if (failed === 0) {
    logger.info('🎉 All HTTPS tests passed! TOS is properly configured.');
  } else {
    logger.warn(`⚠️ ${failed} test(s) failed. TOS HTTPS configuration needs fixing.`);
  }
  
  return failed === 0;
}

// Run tests
if (require.main === module) {
  runHttpsTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      logger.error('💥 HTTPS test runner crashed', { error: error.message });
      process.exit(1);
    });
}

module.exports = {
  testTosHttpsConfiguration,
  testTosEndpointConfiguration,
  runHttpsTests
};
