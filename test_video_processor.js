/**
 * Test VideoProcessor class to verify our fixes
 */

require('dotenv').config();
const { VideoProcessor } = require('./src/worker/videoProcessor');
const { createModuleLogger } = require('./src/common/logger');

const logger = createModuleLogger('videoProcessorTest');

async function testVideoProcessorInitialization() {
  try {
    logger.info('🧪 Testing VideoProcessor initialization...');
    
    const mockJobData = {
      dbId: 'test-123',
      filePath: '/test/path/video.mp4',
      originalName: 'test-video.mp4'
    };
    
    const processor = new VideoProcessor(mockJobData);
    
    // Check if all properties are properly initialized
    const checks = [
      { name: 'dbId', value: processor.dbId, expected: 'test-123' },
      { name: 'tempFiles', value: Array.isArray(processor.tempFiles), expected: true },
      { name: 'audioFiles', value: Array.isArray(processor.audioFiles), expected: true },
      { name: 'tosFiles', value: Array.isArray(processor.tosFiles), expected: true },
      { name: 'asrCompleted', value: processor.asrCompleted, expected: false },
    ];
    
    let allPassed = true;
    
    for (const check of checks) {
      const passed = check.value === check.expected;
      logger.info(`${passed ? '✅' : '❌'} ${check.name}: ${check.value} (expected: ${check.expected})`);
      if (!passed) allPassed = false;
    }
    
    return allPassed;
    
  } catch (error) {
    logger.error('❌ VideoProcessor initialization test failed', { error: error.message });
    return false;
  }
}

async function testCleanupMethods() {
  try {
    logger.info('🧪 Testing cleanup methods...');
    
    const mockJobData = {
      dbId: 'test-cleanup-123',
      filePath: '/test/path/video.mp4',
      originalName: 'test-video.mp4'
    };
    
    const processor = new VideoProcessor(mockJobData);
    
    // Test cleanupAudioFiles with empty array
    await processor.cleanupAudioFiles();
    logger.info('✅ cleanupAudioFiles with empty array - passed');
    
    // Test cleanupTosFiles with empty array
    await processor.cleanupTosFiles();
    logger.info('✅ cleanupTosFiles with empty array - passed');
    
    // Test cleanupTempFiles
    await processor.cleanupTempFiles();
    logger.info('✅ cleanupTempFiles - passed');
    
    // Test with some mock data
    processor.audioFiles = ['/fake/audio1.wav', '/fake/audio2.wav'];
    processor.tosFiles = [
      { key: 'test/key1.wav', url: 'https://example.com/key1.wav', size: 1000 },
      { key: 'test/key2.wav', url: 'https://example.com/key2.wav', size: 2000 }
    ];
    
    // This should not crash even with fake data
    await processor.cleanupAudioFiles();
    logger.info('✅ cleanupAudioFiles with mock data - passed');
    
    await processor.cleanupTosFiles();
    logger.info('✅ cleanupTosFiles with mock data - passed');
    
    return true;
    
  } catch (error) {
    logger.error('❌ Cleanup methods test failed', { error: error.message, stack: error.stack });
    return false;
  }
}

async function testAsrCompletionFlow() {
  try {
    logger.info('🧪 Testing ASR completion flow...');
    
    const mockJobData = {
      dbId: 'test-asr-123',
      filePath: '/test/path/video.mp4',
      originalName: 'test-video.mp4'
    };
    
    const processor = new VideoProcessor(mockJobData);
    
    // Initially ASR should not be completed
    if (processor.asrCompleted !== false) {
      throw new Error('ASR should initially be false');
    }
    logger.info('✅ Initial ASR state - passed');
    
    // Mark ASR as completed
    processor.asrCompleted = true;
    
    // Test cleanup with ASR completed
    await processor.cleanupTempFiles();
    logger.info('✅ Cleanup with ASR completed - passed');
    
    return true;
    
  } catch (error) {
    logger.error('❌ ASR completion flow test failed', { error: error.message });
    return false;
  }
}

async function runAllTests() {
  logger.info('🚀 Starting VideoProcessor tests...');
  
  const tests = [
    { name: 'VideoProcessor Initialization', fn: testVideoProcessorInitialization },
    { name: 'Cleanup Methods', fn: testCleanupMethods },
    { name: 'ASR Completion Flow', fn: testAsrCompletionFlow }
  ];
  
  const results = [];
  
  for (const test of tests) {
    logger.info(`\n📋 Running test: ${test.name}`);
    try {
      const result = await test.fn();
      results.push({ name: test.name, success: result });
      logger.info(`${result ? '✅' : '❌'} Test ${test.name}: ${result ? 'PASSED' : 'FAILED'}`);
    } catch (error) {
      results.push({ name: test.name, success: false, error: error.message });
      logger.error(`❌ Test ${test.name}: FAILED with error`, { error: error.message });
    }
  }
  
  // Summary
  logger.info('\n📊 Test Results Summary:');
  const passed = results.filter(r => r.success).length;
  const failed = results.length - passed;
  
  results.forEach(result => {
    logger.info(`${result.success ? '✅' : '❌'} ${result.name}: ${result.success ? 'PASSED' : 'FAILED'}`);
    if (result.error) {
      logger.info(`   Error: ${result.error}`);
    }
  });
  
  logger.info(`\n🎯 Overall: ${passed}/${results.length} tests passed`);
  
  if (failed === 0) {
    logger.info('🎉 All VideoProcessor tests passed! The fixes are working.');
  } else {
    logger.warn(`⚠️ ${failed} test(s) failed. There are still issues to fix.`);
  }
  
  return failed === 0;
}

// Run tests
if (require.main === module) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      logger.error('💥 Test runner crashed', { error: error.message });
      process.exit(1);
    });
}

module.exports = {
  testVideoProcessorInitialization,
  testCleanupMethods,
  testAsrCompletionFlow,
  runAllTests
};
